//
//  WidgetPlugin.m
//  WidgetPlugin
//
//  Created by uni-app on 2024/01/01.
//

#import "WidgetPlugin.h"
#import <WidgetKit/WidgetKit.h>

@implementation WidgetPlugin

// 更新Widget数据
UNI_EXPORT_METHOD(@selector(updateWidgetData:callback:))
- (void)updateWidgetData:(NSDictionary *)options callback:(UniModuleKeepAliveCallback)callback {
    NSString *title = [options objectForKey:@"title"];
    NSString *content = [options objectForKey:@"content"];
    NSString *timestamp = [options objectForKey:@"timestamp"];
    
    // 使用App Groups共享数据
    NSUserDefaults *sharedDefaults = [[NSUserDefaults alloc] initWithSuiteName:@"group.com.wangxiaobao.iwangke"];
    
    if (title) {
        [sharedDefaults setObject:title forKey:@"widget_title"];
    }
    if (content) {
        [sharedDefaults setObject:content forKey:@"widget_content"];
    }
    if (timestamp) {
        [sharedDefaults setObject:timestamp forKey:@"widget_timestamp"];
    }
    
    [sharedDefaults synchronize];
    
    // 刷新Widget
    if (@available(iOS 14.0, *)) {
        [WidgetCenter.sharedCenter reloadAllTimelines];
    }
    
    if (callback) {
        callback(@{@"success": @YES, @"message": @"Widget data updated successfully"}, NO);
    }
}

// 获取Widget数据
UNI_EXPORT_METHOD(@selector(getWidgetData:))
- (void)getWidgetData:(UniModuleKeepAliveCallback)callback {
    NSUserDefaults *sharedDefaults = [[NSUserDefaults alloc] initWithSuiteName:@"group.com.wangxiaobao.iwangke"];
    
    NSString *title = [sharedDefaults objectForKey:@"widget_title"] ?: @"";
    NSString *content = [sharedDefaults objectForKey:@"widget_content"] ?: @"";
    NSString *timestamp = [sharedDefaults objectForKey:@"widget_timestamp"] ?: @"";
    
    NSDictionary *result = @{
        @"title": title,
        @"content": content,
        @"timestamp": timestamp
    };
    
    if (callback) {
        callback(result, NO);
    }
}

// 检查Widget是否可用
UNI_EXPORT_METHOD(@selector(isWidgetAvailable:))
- (void)isWidgetAvailable:(UniModuleKeepAliveCallback)callback {
    BOOL available = NO;
    if (@available(iOS 14.0, *)) {
        available = YES;
    }
    
    if (callback) {
        callback(@{@"available": @(available)}, NO);
    }
}

@end
