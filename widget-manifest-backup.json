{"注释": "这是启用Widget功能时需要的manifest.json配置", "原生插件配置": {"nativePlugins": {"WidgetPlugin": {"hooksClass": "", "plugins": [{"type": "module", "name": "WidgetPlugin", "class": "WidgetPlugin"}]}}}, "iOS配置": {"ios": {"capabilities": {"entitlements": {"com.apple.security.application-groups": ["group.com.wangxiaobao.iwangke"]}}, "idfa": false, "dSYMs": false}}, "使用说明": {"1": "当您的Apple Developer账号配置好App Groups权限后", "2": "将上述配置添加回manifest.json对应位置", "3": "原生插件配置添加到app-plus.modules后面", "4": "iOS配置替换现有的ios配置", "5": "确保App Groups标识符与您的开发者账号一致"}}