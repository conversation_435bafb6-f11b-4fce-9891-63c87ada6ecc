# 制作自定义调试基座指南

## 🎯 问题说明

当您看到以下错误时：
```
当前运行的基座不包含原生插件[WidgetPlugin]，请在manifest中配置该插件，重新制作包括该原生插件的自定义运行基座
```

这表示您需要制作一个包含Widget插件的自定义调试基座。

## 📱 制作自定义基座步骤

### 1. 在HBuilderX中制作基座

1. **打开项目**
   - 确保WidgetDemo2项目在HBuilderX中打开
   - 确认manifest.json已正确配置Widget插件

2. **开始制作基座**
   ```
   菜单路径：运行 → 运行到手机或模拟器 → 制作自定义调试基座
   ```

3. **选择平台**
   - 选择 "iOS" 平台
   - 确认证书配置正确

4. **等待制作完成**
   - 制作过程可能需要几分钟
   - 请耐心等待，不要中断过程

### 2. 证书配置要求

**重要：确保您的证书包含以下配置**

1. **App Groups权限**
   - 证书必须包含 `group.com.wangxiaobao.iwangke` 权限
   - 在Apple Developer后台确认配置正确

2. **Bundle ID匹配**
   - 确保证书的Bundle ID与项目配置一致
   - 检查是否有通配符证书可用

### 3. 制作过程中可能遇到的问题

#### 问题1：证书权限不匹配
```
错误：Provisioning profile doesn't match entitlements
解决：更新证书，确保包含App Groups权限
```

#### 问题2：Bundle ID不匹配
```
错误：Bundle identifier doesn't match
解决：检查证书Bundle ID配置
```

#### 问题3：网络连接问题
```
错误：网络连接超时
解决：检查网络连接，重试制作过程
```

## 🔧 临时解决方案

如果暂时无法制作自定义基座，项目已经做了兼容处理：

1. **应用仍可正常运行**
   - 主要功能不受影响
   - Widget功能会显示为"不可用"状态

2. **友好的提示信息**
   - 界面会显示制作基座的提示
   - 不会出现崩溃或错误

## 📋 制作成功后的验证

### 1. 重新运行项目
```bash
# 基座制作完成后
1. 重新运行项目到手机
2. 查看控制台日志
3. 确认Widget插件加载成功
```

### 2. 功能验证
- ✅ Widget状态显示为"可用"
- ✅ 可以更新Widget数据
- ✅ 可以获取Widget数据
- ✅ 按钮功能正常

### 3. 日志确认
正常情况下应该看到：
```
Widget available: true
```

## 🚀 使用新基座

### 1. 安装新基座
- HBuilderX会自动安装新制作的基座
- 如果没有自动安装，手动安装基座App

### 2. 运行项目
- 使用新基座运行项目
- 确认Widget功能正常工作

### 3. 测试Widget功能
1. **检查Widget状态**
   - 应该显示"✅ Widget可用"

2. **测试数据更新**
   - 输入标题和内容
   - 点击"更新Widget"按钮
   - 确认操作成功

3. **添加Widget到桌面**
   - 长按iOS桌面
   - 添加Widget
   - 验证显示效果

## 📝 注意事项

### 1. 基座有效期
- 自定义基座有一定的有效期
- 过期后需要重新制作

### 2. 证书更新
- 如果更新了证书，需要重新制作基座
- 确保新证书包含所需权限

### 3. 插件更新
- 如果修改了原生插件代码
- 需要重新制作基座才能生效

## 🔍 常见问题排查

### Q1: 基座制作失败
**A:** 检查以下项目：
- 网络连接是否正常
- 证书配置是否正确
- manifest.json配置是否有误

### Q2: Widget功能仍不可用
**A:** 确认以下步骤：
- 是否使用了新制作的基座
- 是否重新安装了基座App
- 是否重启了应用

### Q3: 证书权限问题
**A:** 解决方法：
- 登录Apple Developer后台
- 确认App Groups配置正确
- 重新生成Provisioning Profile

## 📞 获取帮助

如果在制作基座过程中遇到问题：

1. **查看HBuilderX控制台**
   - 详细的错误信息
   - 制作过程日志

2. **检查项目配置**
   - manifest.json配置
   - 证书和权限设置

3. **参考相关文档**
   - `widget-config.md` - 详细配置说明
   - `README.md` - 项目使用指南
