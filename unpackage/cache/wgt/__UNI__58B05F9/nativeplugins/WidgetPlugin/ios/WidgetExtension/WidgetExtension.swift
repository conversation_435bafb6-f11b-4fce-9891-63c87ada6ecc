//
//  WidgetExtension.swift
//  WidgetExtension
//
//  Created by uni-app on 2024/01/01.
//

import WidgetKit
import SwiftUI

// Widget数据模型
struct WidgetData {
    let title: String
    let content: String
    let timestamp: String
    
    static let placeholder = WidgetData(
        title: "Widget Demo",
        content: "这是一个示例Widget",
        timestamp: "刚刚"
    )
}

// Timeline Provider
struct WidgetTimelineProvider: TimelineProvider {
    func placeholder(in context: Context) -> WidgetEntry {
        WidgetEntry(date: Date(), data: WidgetData.placeholder)
    }
    
    func getSnapshot(in context: Context, completion: @escaping (WidgetEntry) -> ()) {
        let entry = WidgetEntry(date: Date(), data: loadWidgetData())
        completion(entry)
    }
    
    func getTimeline(in context: Context, completion: @escaping (Timeline<WidgetEntry>) -> ()) {
        let currentDate = Date()
        let data = loadWidgetData()
        let entry = WidgetEntry(date: currentDate, data: data)
        
        // 设置下次更新时间（15分钟后）
        let nextUpdate = Calendar.current.date(byAdding: .minute, value: 15, to: currentDate)!
        let timeline = Timeline(entries: [entry], policy: .after(nextUpdate))
        
        completion(timeline)
    }
    
    private func loadWidgetData() -> WidgetData {
        let sharedDefaults = UserDefaults(suiteName: "group.widgetdemo.shared")
        
        let title = sharedDefaults?.string(forKey: "widget_title") ?? "Widget Demo"
        let content = sharedDefaults?.string(forKey: "widget_content") ?? "暂无数据"
        let timestamp = sharedDefaults?.string(forKey: "widget_timestamp") ?? "未知"
        
        return WidgetData(title: title, content: content, timestamp: timestamp)
    }
}

// Widget Entry
struct WidgetEntry: TimelineEntry {
    let date: Date
    let data: WidgetData
}

// Widget View
struct WidgetExtensionEntryView: View {
    var entry: WidgetTimelineProvider.Entry
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "app.fill")
                    .foregroundColor(.blue)
                    .font(.title2)
                
                Text(entry.data.title)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .lineLimit(1)
                
                Spacer()
            }
            
            Text(entry.data.content)
                .font(.body)
                .foregroundColor(.secondary)
                .lineLimit(3)
            
            Spacer()
            
            HStack {
                Spacer()
                Text(entry.data.timestamp)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }
}

// Widget Configuration
@main
struct WidgetExtension: Widget {
    let kind: String = "WidgetExtension"
    
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: WidgetTimelineProvider()) { entry in
            WidgetExtensionEntryView(entry: entry)
        }
        .configurationDisplayName("Widget Demo")
        .description("这是一个uni-app Widget演示")
        .supportedFamilies([.systemSmall, .systemMedium])
    }
}
