if("undefined"==typeof Promise||Promise.prototype.finally||(Promise.prototype.finally=function(e){const t=this.constructor;return this.then((a=>t.resolve(e()).then((()=>a))),(a=>t.resolve(e()).then((()=>{throw a}))))}),"undefined"!=typeof uni&&uni&&uni.requireGlobal){const e=uni.requireGlobal();ArrayBuffer=e.<PERSON>,Int8Array=e.Int8Array,Uint8Array=e.Uint8Array,Uint8ClampedArray=e.Uint8ClampedArray,Int16Array=e.Int16Array,Uint16Array=e.Uint16Array,Int32Array=e.Int32Array,Uint32Array=e.Uint32Array,Float32Array=e.Float32Array,Float64Array=e.Float64Array,BigInt64Array=e.BigInt64Array,BigUint64Array=e.BigUint64Array}uni.restoreGlobal&&uni.restoreGlobal(Vue,weex,plus,setTimeout,clearTimeout,setInterval,clearInterval),function(e){"use strict";function t(e){return weex.requireModule(e)}function a(e,t,...a){uni.__log__?uni.__log__(e,t,...a):console[e].apply(console,[...a,t])}const i=t("WidgetPlugin");class n{static isAvailable(){return new Promise(((e,t)=>{i?i.isWidgetAvailable((t=>{e(t.available||!1)})):e(!1)}))}static updateData(e){return new Promise(((t,a)=>{if(!i)return void a(new Error("Widget plugin not available"));const n={title:e.title||"",content:e.content||"",timestamp:e.timestamp||(new Date).toLocaleString()};i.updateWidgetData(n,(e=>{e.success?t(e):a(new Error(e.message||"Update failed"))}))}))}static getData(){return new Promise(((e,t)=>{i?i.getWidgetData((t=>{e(t)})):t(new Error("Widget plugin not available"))}))}static updateTitle(e){return this.updateData({title:e})}static updateContent(e){return this.updateData({content:e})}static updateAll(e,t){return this.updateData({title:e,content:t,timestamp:(new Date).toLocaleString()})}}__definePage("pages/index/index",((e,t)=>{const a=e.__vccOpts||e;for(const[i,n]of t)a[i]=n;return a})({data:()=>({title:"Widget Demo",widgetAvailable:!1,widgetTitle:"Hello Widget",widgetContent:"这是来自uni-app的Widget内容",currentWidgetData:null}),async onLoad(){t?(await this.checkWidgetStatus(),await this.getWidgetData()):(a("log","at pages/index/index.vue:103","当前基座不支持原生插件，请制作自定义基座"),this.widgetAvailable=!1)},methods:{async checkWidgetStatus(){try{if(!t("WidgetPlugin"))return a("log","at pages/index/index.vue:114","WidgetPlugin not available in current base"),void(this.widgetAvailable=!1);this.widgetAvailable=await n.isAvailable(),a("log","at pages/index/index.vue:120","Widget available:",this.widgetAvailable)}catch(e){a("error","at pages/index/index.vue:122","Check widget status error:",e),this.widgetAvailable=!1}},async updateWidget(){if(this.widgetAvailable)try{uni.showLoading({title:"更新中..."});await n.updateAll(this.widgetTitle,this.widgetContent);uni.hideLoading(),uni.showToast({title:"更新成功",icon:"success"}),await this.getWidgetData()}catch(e){uni.hideLoading(),a("error","at pages/index/index.vue:157","Update widget error:",e),uni.showToast({title:"更新失败",icon:"none"})}else uni.showToast({title:"Widget不可用",icon:"none"})},async getWidgetData(){if(this.widgetAvailable)try{const e=await n.getData();this.currentWidgetData=e,a("log","at pages/index/index.vue:174","Widget data:",e)}catch(e){a("error","at pages/index/index.vue:176","Get widget data error:",e)}}}},[["render",function(t,a,i,n,l,r){return e.openBlock(),e.createElementBlock("view",{class:"content"},[e.createElementVNode("image",{class:"logo",src:"/static/logo.png"}),e.createElementVNode("view",{class:"text-area"},[e.createElementVNode("text",{class:"title"},e.toDisplayString(l.title),1)]),e.createElementVNode("view",{class:"widget-panel"},[e.createElementVNode("text",{class:"panel-title"},"iOS Widget 控制面板"),e.createElementVNode("view",{class:"status-section"},[e.createElementVNode("text",{class:"section-title"},"Widget状态"),e.createElementVNode("text",{class:e.normalizeClass(["status-text",l.widgetAvailable?"status-available":"status-unavailable"])},e.toDisplayString(l.widgetAvailable?"✅ Widget可用":"❌ Widget不可用"),3),l.widgetAvailable?e.createCommentVNode("",!0):(e.openBlock(),e.createElementBlock("text",{key:0,class:"status-tip"}," 💡 如需使用Widget功能，请制作包含原生插件的自定义调试基座 "))]),e.createElementVNode("view",{class:"input-section"},[e.createElementVNode("text",{class:"section-title"},"更新Widget数据"),e.withDirectives(e.createElementVNode("input",{class:"input-field","onUpdate:modelValue":a[0]||(a[0]=e=>l.widgetTitle=e),placeholder:"输入Widget标题",maxlength:"50"},null,512),[[e.vModelText,l.widgetTitle]]),e.withDirectives(e.createElementVNode("textarea",{class:"textarea-field","onUpdate:modelValue":a[1]||(a[1]=e=>l.widgetContent=e),placeholder:"输入Widget内容",maxlength:"200"},null,512),[[e.vModelText,l.widgetContent]])]),e.createElementVNode("view",{class:"button-section"},[e.createElementVNode("button",{class:"btn btn-primary",onClick:a[2]||(a[2]=(...e)=>r.updateWidget&&r.updateWidget(...e)),disabled:!l.widgetAvailable}," 更新Widget ",8,["disabled"]),e.createElementVNode("button",{class:"btn btn-secondary",onClick:a[3]||(a[3]=(...e)=>r.getWidgetData&&r.getWidgetData(...e)),disabled:!l.widgetAvailable}," 获取Widget数据 ",8,["disabled"]),e.createElementVNode("button",{class:"btn btn-info",onClick:a[4]||(a[4]=(...e)=>r.checkWidgetStatus&&r.checkWidgetStatus(...e))}," 检查Widget状态 ")]),l.currentWidgetData?(e.openBlock(),e.createElementBlock("view",{key:0,class:"data-section"},[e.createElementVNode("text",{class:"section-title"},"当前Widget数据"),e.createElementVNode("view",{class:"data-item"},[e.createElementVNode("text",{class:"data-label"},"标题："),e.createElementVNode("text",{class:"data-value"},e.toDisplayString(l.currentWidgetData.title),1)]),e.createElementVNode("view",{class:"data-item"},[e.createElementVNode("text",{class:"data-label"},"内容："),e.createElementVNode("text",{class:"data-value"},e.toDisplayString(l.currentWidgetData.content),1)]),e.createElementVNode("view",{class:"data-item"},[e.createElementVNode("text",{class:"data-label"},"更新时间："),e.createElementVNode("text",{class:"data-value"},e.toDisplayString(l.currentWidgetData.timestamp),1)])])):e.createCommentVNode("",!0)])])}]]));const l={onLaunch:function(){a("log","at App.vue:4","App Launch")},onShow:function(){a("log","at App.vue:7","App Show")},onHide:function(){a("log","at App.vue:10","App Hide")}};const{app:r,Vuex:o,Pinia:s}={app:e.createVueApp(l)};uni.Vuex=o,uni.Pinia=s,r.provide("__globalStyles",__uniConfig.styles),r._component.mpType="app",r._component.render=()=>{},r.mount("#app")}(Vue);
