{"program": {"fileNames": ["../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/hbuilderx-language-services/builtin-dts/uts-types/common/array.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/hbuilderx-language-services/builtin-dts/uts-types/common/boolean.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/hbuilderx-language-services/builtin-dts/uts-types/common/console.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/hbuilderx-language-services/builtin-dts/uts-types/common/date.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/hbuilderx-language-services/builtin-dts/uts-types/common/error.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/hbuilderx-language-services/builtin-dts/uts-types/common/global.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/hbuilderx-language-services/builtin-dts/uts-types/common/json.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/hbuilderx-language-services/builtin-dts/uts-types/common/map.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/hbuilderx-language-services/builtin-dts/uts-types/common/math.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/hbuilderx-language-services/builtin-dts/uts-types/common/number.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/hbuilderx-language-services/builtin-dts/uts-types/common/regexp.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/hbuilderx-language-services/builtin-dts/uts-types/common/set.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/hbuilderx-language-services/builtin-dts/uts-types/common/string.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/hbuilderx-language-services/builtin-dts/uts-types/common/timers.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/hbuilderx-language-services/builtin-dts/uts-types/common/utsjsonobject.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/hbuilderx-language-services/builtin-dts/uts-types/common/arraybuffer.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/hbuilderx-language-services/builtin-dts/uts-types/common/float32array.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/hbuilderx-language-services/builtin-dts/uts-types/common/float64array.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/hbuilderx-language-services/builtin-dts/uts-types/common/int8array.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/hbuilderx-language-services/builtin-dts/uts-types/common/int16array.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/hbuilderx-language-services/builtin-dts/uts-types/common/int32array.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/hbuilderx-language-services/builtin-dts/uts-types/common/uint8array.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/hbuilderx-language-services/builtin-dts/uts-types/common/uint8clampedarray.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/hbuilderx-language-services/builtin-dts/uts-types/common/uint16array.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/hbuilderx-language-services/builtin-dts/uts-types/common/uint32array.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/hbuilderx-language-services/builtin-dts/uts-types/common/dataview.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/hbuilderx-language-services/builtin-dts/uts-types/common/iterable.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/hbuilderx-language-services/builtin-dts/uts-types/common/common.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/uniapp-uts-v1/node_modules/@dcloudio/uni-uts-v1/lib/uts/types/uts/shims/shims.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/uniapp-uts-v1/node_modules/@dcloudio/uni-uts-v1/lib/uts/types/uts/shims/lib.es5.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/uniapp-uts-v1/node_modules/@dcloudio/uni-uts-v1/lib/uts/types/uts/shims/lib.es2015.collection.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/uniapp-uts-v1/node_modules/@dcloudio/uni-uts-v1/lib/uts/types/uts/shims/lib.es2015.promise.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/uniapp-uts-v1/node_modules/@dcloudio/uni-uts-v1/lib/uts/types/uts/shims/lib.es2015.symbol.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/uniapp-uts-v1/node_modules/@dcloudio/uni-uts-v1/lib/uts/types/uts/shims/lib.es2015.symbol.wellknown.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/uniapp-uts-v1/node_modules/@dcloudio/uni-uts-v1/lib/uts/types/uts/shims/lib.es2015.iterable.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/uniapp-uts-v1/node_modules/@dcloudio/uni-uts-v1/lib/uts/types/uts/shims/lib.es2018.asynciterable.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/uniapp-uts-v1/node_modules/@dcloudio/uni-uts-v1/lib/uts/types/uts/shims/lib.es2018.asyncgenerator.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/uniapp-uts-v1/node_modules/@dcloudio/uni-uts-v1/lib/uts/types/uts/shims/lib.es2018.promise.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/uniapp-uts-v1/node_modules/@dcloudio/uni-uts-v1/lib/uts/types/uts/shims/lib.es2020.symbol.wellknown.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/uniapp-uts-v1/node_modules/@dcloudio/uni-uts-v1/lib/uts/types/uts/shims/index.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/uniapp-uts-v1/node_modules/@dcloudio/uni-uts-v1/lib/uts/types/uts/index.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/uniapp-uts-v1/node_modules/@dcloudio/uni-uts-v1/lib/uts/types/uni-x/hbuilder-x/hbuilderx.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/uniapp-uts-v1/node_modules/@dcloudio/uni-uts-v1/lib/uts/types/uni-x/hbuilder-x/index.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/uniapp-uts-v1/node_modules/@dcloudio/uni-uts-v1/lib/uts/types/uni-x/shims/common.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/hbuilderx-language-services/builtin-dts/uts-types/app-ios/utsios.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/hbuilderx-language-services/builtin-dts/uts-types/app-ios/utsioshookproxy.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/hbuilderx-language-services/builtin-dts/uts-types/app-js/utsjs.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/hbuilderx-language-services/builtin-dts/uts-types/app-ios/index.d.ts", "../../../../../../../../../../../../applications/hbuilderx.app/contents/hbuilderx/plugins/uniapp-uts-v1/node_modules/@dcloudio/uni-uts-v1/lib/uts/types/uni-x/app-ios.d.ts"], "fileInfos": [{"version": "f5a714ada3afeb4198e77ac4cbdf0e0502ab9b075421a9efa3f9899c3a6d285c", "affectsGlobalScope": true}, {"version": "97e24360a88a41bc31c34846db167a5068460d3a8b92025184c8ea39ae424314", "affectsGlobalScope": true}, {"version": "b7d7a137a20074fbdddbc636efdd42d61367d835ccc6fcf6124863e5d32bf11c", "affectsGlobalScope": true}, {"version": "75f819217481ed0ecfdfdd9c354b04d01ef0e6414b77abbf3a67f1e745344a57", "affectsGlobalScope": true}, {"version": "173b34be3df2099c2da11fb3ceecf87e883bd64f5219c0ee7bc6add9bc812cde", "affectsGlobalScope": true}, {"version": "d9791f8164b897a7e82315b3287f145aef32d81aa73752c2255e0e143b546600", "affectsGlobalScope": true}, {"version": "68afb9fac7270a141c36991c639df38e9d8275bf6c55f2f33be061bb194780b3", "affectsGlobalScope": true}, {"version": "0979c2b522cc005ebb3fb3c7dc2b10f4abe5de3b2bbbec2186f9a832ddeb3c3c", "affectsGlobalScope": true}, {"version": "09c49e401e4fdd6bcfb1e2d5aa1eabe031da11bafde97c298e0357a97994d863", "affectsGlobalScope": true}, {"version": "a67361be5fc747096553d83260ebbd589a43e09ed511bef6804919b555680827", "affectsGlobalScope": true}, {"version": "9495bc35902c251663236a05fe617b411f0590cf46a4962c84ac4cfc406fab3e", "affectsGlobalScope": true}, {"version": "79758ed63541ad9f79fef6d04cdd791bfed52d639b71d1c3cf406126ba4c961b", "affectsGlobalScope": true}, {"version": "0406f954cd60927c393a1dd2cd4bfb8355860bc41bbe8024e628a0f1d755de6c", "affectsGlobalScope": true}, {"version": "679691d8dfbfd8ab8d87e81b261f40886c05e9a9539da21db1e58a198e3084a3", "affectsGlobalScope": true}, {"version": "bf3de718b9d34d05ea8b7c0172063257e7a89f1a2e15d66de826814586da7ce4", "affectsGlobalScope": true}, {"version": "0aca09a3a690438ac20a824d8236bfdb84e4035724e77073c7f144b18339ec65", "affectsGlobalScope": true}, {"version": "1acbd1d3afb34b522e43e567acf76381af1b858055f47c0ceedd858542426f0f", "affectsGlobalScope": true}, {"version": "e62d4c55b645f4d9b8627bdb6e04ab641d25abc48b27a68983963296fcee1300", "affectsGlobalScope": true}, {"version": "a5a65d5d74cac1e1e27de4adc0ab37048332d91be0fd914209ca04ccd63b4141", "affectsGlobalScope": true}, {"version": "5eb86cedb0d685b8c1d1b51d2892402ecd6e0cff047ba3e683bc7cbc585ebd9b", "affectsGlobalScope": true}, {"version": "cb4d3f49248d601600b9e5e6268c3a1925a0e3d3a6b13ff7e178924fc7763aa4", "affectsGlobalScope": true}, {"version": "7ce21134b8a21e2672f56ceda596d33dc08f27a9900ec068a33dd471667a0dd9", "affectsGlobalScope": true}, {"version": "105e17a5ad5e5fcf937f1a7412b849c67d98e17aa6ac257baf988a56be4d23de", "affectsGlobalScope": true}, {"version": "471ea135c34237d3fcc6918a297c21e321cd99e20ac29673506590c0e91d10d0", "affectsGlobalScope": true}, {"version": "6c71e7f5dcdf436e701fee0c76995e197f1b8b44ed64119881c04ad30c432513", "affectsGlobalScope": true}, {"version": "bfea9c54c2142652e7f2f09b7b395c57f3e7650fb2981d9f183de9eeae8a1487", "affectsGlobalScope": true}, {"version": "97f9a7c19d36f4d587e9d2167be936a36f92ad4f0545e4fde13abf03ffa2c836", "affectsGlobalScope": true}, "db8eb85d3f5c85cc8b2b051fde29f227ec8fbe50fd53c0dc5fc7a35b0209de4a", {"version": "a40c58a6b116c6be65fa18892baa003de697e8bb588738086eeb66767086d795", "affectsGlobalScope": true}, {"version": "93d5d3abbc921afca6c084cfc8eff9a4ce39a2f8444f49984e5ae9242346676b", "affectsGlobalScope": true}, {"version": "b3c68a2799ae071e2cb60df38a22a44e2bc8875c03b58a84ca500af60e23770e", "affectsGlobalScope": true}, {"version": "a46622c46620d4dfe66fa8953348a4c0788b7a6773ad38fdec7af31643997eb9", "affectsGlobalScope": true}, {"version": "2e44c5149ca592f354a625e685feb2ba572edc16938c909d8d08670a78cce004", "affectsGlobalScope": true}, {"version": "8a0d41bfa6df25a73dbcf0d3a2cf3977709816dc916e7d94e6ca4c92ff405cee", "affectsGlobalScope": true}, {"version": "46906cc6e235f0fd29f2014f46416a3cb5328888c0f72677a44329235bdb98bf", "affectsGlobalScope": true}, {"version": "b5f7531cbb9bc05d7512a44929944e20f86da88059f9e91b551bcb7031233f14", "affectsGlobalScope": true}, {"version": "c3af388ae7dc7da32b83299506e302fb66b6471e17bc270a3a4779655710f74f", "affectsGlobalScope": true}, {"version": "194e09f34e64f1c9458d2a2e0281752ce194a4e67408c6eda4c171276c6a543d", "affectsGlobalScope": true}, {"version": "530ab35b2cef5150ec75e6c3d57c535ef7d9be55bdbc4cd24b9572d468bcb16a", "affectsGlobalScope": true}, "e79f3f68452f3f00ffaf4d89f79ee18b4f6c8635383b1f0a514f1a126ac37345", "465c0d2284773bd856e497cd50f5ad893a4f44b739b2288b12480196ff05dcf8", {"version": "12b835d3220c33d4c7c07382634f8955fe10cc79e043fadcc7bdbc083babbaa5", "affectsGlobalScope": true}, "e11bab81f0ae2021975aff038d7c1a5cf6e939ca8b986976c852f282cddecc0a", {"version": "3563038fd2b36ecea3f3de752ebb708b59ef78bb4a01a281cacc7fe346b5e345", "affectsGlobalScope": true}, {"version": "3c0c6adb3a2ea6d35bbdcc5ef3891c38f2abb81b2b88c7a999a19fa8fa5b45bc", "affectsGlobalScope": true}, "4aa419dfdb3704650808b0ae569258d9f4ed4b0da0fbedf7c59f3300b3ce038a", {"version": "b9241ecb5024beeaeb98fb558000dbc55e650576e572d194508f52807af6bcba", "affectsGlobalScope": true}, "57254dac9873cfaf8e1b6aa58a3ef928ca9ef3f9e2bc7265e249d7f8ce802e34", "2f97f585910f59c2c053ac4a70f5a4cf39d54a960239f3b92a9994272c90a8c9"], "root": [49], "options": {"inlineSources": true, "module": 99, "noEmitOnError": false, "noImplicitAny": false, "noImplicitThis": true, "outDir": "../../../../.uvue/app-ios", "rootDir": "../../../../.tsc/app-ios", "skipLibCheck": true, "sourceMap": true, "strict": true, "target": 99, "tsBuildInfoFile": "./.tsbuildInfo", "useDefineForClassFields": false}, "fileIdsList": [[45, 46, 47], [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27], [41, 44], [42], [29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39], [33], [36], [33, 35]], "referencedMap": [[48, 1], [28, 2], [49, 3], [43, 4], [40, 5], [35, 6], [34, 6], [37, 7], [36, 8], [39, 8]], "exportedModulesMap": [[48, 1], [28, 2], [49, 3], [43, 4], [40, 5], [35, 6], [34, 6], [37, 7], [36, 8], [39, 8]], "semanticDiagnosticsPerFile": [48, 45, 46, 47, 1, 16, 2, 28, 3, 26, 4, 5, 17, 18, 6, 20, 21, 19, 27, 7, 8, 9, 10, 11, 12, 13, 14, 24, 25, 22, 23, 15, 49, 42, 43, 44, 41, 40, 31, 35, 32, 33, 34, 37, 36, 38, 39, 30, 29]}, "version": "5.2.2"}