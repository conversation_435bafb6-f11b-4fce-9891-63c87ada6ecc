if (typeof Promise !== "undefined" && !Promise.prototype.finally) {
  Promise.prototype.finally = function(callback) {
    const promise = this.constructor;
    return this.then(
      (value) => promise.resolve(callback()).then(() => value),
      (reason) => promise.resolve(callback()).then(() => {
        throw reason;
      })
    );
  };
}
;
if (typeof uni !== "undefined" && uni && uni.requireGlobal) {
  const global = uni.requireGlobal();
  ArrayBuffer = global.ArrayBuffer;
  Int8Array = global.Int8Array;
  Uint8Array = global.Uint8Array;
  Uint8ClampedArray = global.Uint8ClampedArray;
  Int16Array = global.Int16Array;
  Uint16Array = global.Uint16Array;
  Int32Array = global.Int32Array;
  Uint32Array = global.Uint32Array;
  Float32Array = global.Float32Array;
  Float64Array = global.Float64Array;
  BigInt64Array = global.BigInt64Array;
  BigUint64Array = global.BigUint64Array;
}
;
if (uni.restoreGlobal) {
  uni.restoreGlobal(Vue, weex, plus, setTimeout, clearTimeout, setInterval, clearInterval);
}
(function(vue) {
  "use strict";
  function requireNativePlugin(name) {
    return weex.requireModule(name);
  }
  function formatAppLog(type, filename, ...args) {
    if (uni.__log__) {
      uni.__log__(type, filename, ...args);
    } else {
      console[type].apply(console, [...args, filename]);
    }
  }
  const WidgetPlugin = requireNativePlugin("WidgetPlugin");
  class WidgetManager {
    /**
     * 检查Widget是否可用
     * @returns {Promise<boolean>}
     */
    static isAvailable() {
      return new Promise((resolve, reject) => {
        if (!WidgetPlugin) {
          resolve(false);
          return;
        }
        WidgetPlugin.isWidgetAvailable((result) => {
          resolve(result.available || false);
        });
      });
    }
    /**
     * 更新Widget数据
     * @param {Object} data - Widget数据
     * @param {string} data.title - 标题
     * @param {string} data.content - 内容
     * @param {string} data.timestamp - 时间戳
     * @returns {Promise<Object>}
     */
    static updateData(data) {
      return new Promise((resolve, reject) => {
        if (!WidgetPlugin) {
          reject(new Error("Widget plugin not available"));
          return;
        }
        const options = {
          title: data.title || "",
          content: data.content || "",
          timestamp: data.timestamp || (/* @__PURE__ */ new Date()).toLocaleString()
        };
        WidgetPlugin.updateWidgetData(options, (result) => {
          if (result.success) {
            resolve(result);
          } else {
            reject(new Error(result.message || "Update failed"));
          }
        });
      });
    }
    /**
     * 获取Widget数据
     * @returns {Promise<Object>}
     */
    static getData() {
      return new Promise((resolve, reject) => {
        if (!WidgetPlugin) {
          reject(new Error("Widget plugin not available"));
          return;
        }
        WidgetPlugin.getWidgetData((result) => {
          resolve(result);
        });
      });
    }
    /**
     * 更新Widget标题
     * @param {string} title - 新标题
     * @returns {Promise<Object>}
     */
    static updateTitle(title) {
      return this.updateData({ title });
    }
    /**
     * 更新Widget内容
     * @param {string} content - 新内容
     * @returns {Promise<Object>}
     */
    static updateContent(content) {
      return this.updateData({ content });
    }
    /**
     * 批量更新Widget数据
     * @param {string} title - 标题
     * @param {string} content - 内容
     * @returns {Promise<Object>}
     */
    static updateAll(title, content) {
      return this.updateData({
        title,
        content,
        timestamp: (/* @__PURE__ */ new Date()).toLocaleString()
      });
    }
  }
  const _imports_0 = "/static/logo.png";
  const _export_sfc = (sfc, props) => {
    const target = sfc.__vccOpts || sfc;
    for (const [key, val] of props) {
      target[key] = val;
    }
    return target;
  };
  const _sfc_main$1 = {
    data() {
      return {
        title: "Widget Demo",
        widgetAvailable: false,
        widgetTitle: "Hello Widget",
        widgetContent: "这是来自uni-app的Widget内容",
        currentWidgetData: null
      };
    },
    async onLoad() {
      await this.checkWidgetStatus();
      await this.getWidgetData();
    },
    methods: {
      // 检查Widget状态
      async checkWidgetStatus() {
        try {
          this.widgetAvailable = await WidgetManager.isAvailable();
          formatAppLog("log", "at pages/index/index.vue:103", "Widget available:", this.widgetAvailable);
        } catch (error) {
          formatAppLog("error", "at pages/index/index.vue:105", "Check widget status error:", error);
          this.widgetAvailable = false;
        }
      },
      // 更新Widget数据
      async updateWidget() {
        if (!this.widgetAvailable) {
          uni.showToast({
            title: "Widget不可用",
            icon: "none"
          });
          return;
        }
        try {
          uni.showLoading({
            title: "更新中..."
          });
          const result = await WidgetManager.updateAll(
            this.widgetTitle,
            this.widgetContent
          );
          uni.hideLoading();
          uni.showToast({
            title: "更新成功",
            icon: "success"
          });
          await this.getWidgetData();
        } catch (error) {
          uni.hideLoading();
          formatAppLog("error", "at pages/index/index.vue:140", "Update widget error:", error);
          uni.showToast({
            title: "更新失败",
            icon: "none"
          });
        }
      },
      // 获取Widget数据
      async getWidgetData() {
        if (!this.widgetAvailable) {
          return;
        }
        try {
          const data = await WidgetManager.getData();
          this.currentWidgetData = data;
          formatAppLog("log", "at pages/index/index.vue:157", "Widget data:", data);
        } catch (error) {
          formatAppLog("error", "at pages/index/index.vue:159", "Get widget data error:", error);
        }
      }
    }
  };
  function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
    return vue.openBlock(), vue.createElementBlock("view", { class: "content" }, [
      vue.createElementVNode("image", {
        class: "logo",
        src: _imports_0
      }),
      vue.createElementVNode("view", { class: "text-area" }, [
        vue.createElementVNode(
          "text",
          { class: "title" },
          vue.toDisplayString($data.title),
          1
          /* TEXT */
        )
      ]),
      vue.createCommentVNode(" Widget控制面板 "),
      vue.createElementVNode("view", { class: "widget-panel" }, [
        vue.createElementVNode("text", { class: "panel-title" }, "iOS Widget 控制面板"),
        vue.createCommentVNode(" Widget状态 "),
        vue.createElementVNode("view", { class: "status-section" }, [
          vue.createElementVNode("text", { class: "section-title" }, "Widget状态"),
          vue.createElementVNode(
            "text",
            {
              class: vue.normalizeClass(["status-text", $data.widgetAvailable ? "status-available" : "status-unavailable"])
            },
            vue.toDisplayString($data.widgetAvailable ? "✅ Widget可用" : "❌ Widget不可用"),
            3
            /* TEXT, CLASS */
          )
        ]),
        vue.createCommentVNode(" 数据输入 "),
        vue.createElementVNode("view", { class: "input-section" }, [
          vue.createElementVNode("text", { class: "section-title" }, "更新Widget数据"),
          vue.withDirectives(vue.createElementVNode(
            "input",
            {
              class: "input-field",
              "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => $data.widgetTitle = $event),
              placeholder: "输入Widget标题",
              maxlength: "50"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.widgetTitle]
          ]),
          vue.withDirectives(vue.createElementVNode(
            "textarea",
            {
              class: "textarea-field",
              "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => $data.widgetContent = $event),
              placeholder: "输入Widget内容",
              maxlength: "200"
            },
            null,
            512
            /* NEED_PATCH */
          ), [
            [vue.vModelText, $data.widgetContent]
          ])
        ]),
        vue.createCommentVNode(" 操作按钮 "),
        vue.createElementVNode("view", { class: "button-section" }, [
          vue.createElementVNode("button", {
            class: "btn btn-primary",
            onClick: _cache[2] || (_cache[2] = (...args) => $options.updateWidget && $options.updateWidget(...args)),
            disabled: !$data.widgetAvailable
          }, " 更新Widget ", 8, ["disabled"]),
          vue.createElementVNode("button", {
            class: "btn btn-secondary",
            onClick: _cache[3] || (_cache[3] = (...args) => $options.getWidgetData && $options.getWidgetData(...args)),
            disabled: !$data.widgetAvailable
          }, " 获取Widget数据 ", 8, ["disabled"]),
          vue.createElementVNode("button", {
            class: "btn btn-info",
            onClick: _cache[4] || (_cache[4] = (...args) => $options.checkWidgetStatus && $options.checkWidgetStatus(...args))
          }, " 检查Widget状态 ")
        ]),
        vue.createCommentVNode(" 当前Widget数据显示 "),
        $data.currentWidgetData ? (vue.openBlock(), vue.createElementBlock("view", {
          key: 0,
          class: "data-section"
        }, [
          vue.createElementVNode("text", { class: "section-title" }, "当前Widget数据"),
          vue.createElementVNode("view", { class: "data-item" }, [
            vue.createElementVNode("text", { class: "data-label" }, "标题："),
            vue.createElementVNode(
              "text",
              { class: "data-value" },
              vue.toDisplayString($data.currentWidgetData.title),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "data-item" }, [
            vue.createElementVNode("text", { class: "data-label" }, "内容："),
            vue.createElementVNode(
              "text",
              { class: "data-value" },
              vue.toDisplayString($data.currentWidgetData.content),
              1
              /* TEXT */
            )
          ]),
          vue.createElementVNode("view", { class: "data-item" }, [
            vue.createElementVNode("text", { class: "data-label" }, "更新时间："),
            vue.createElementVNode(
              "text",
              { class: "data-value" },
              vue.toDisplayString($data.currentWidgetData.timestamp),
              1
              /* TEXT */
            )
          ])
        ])) : vue.createCommentVNode("v-if", true)
      ])
    ]);
  }
  const PagesIndexIndex = /* @__PURE__ */ _export_sfc(_sfc_main$1, [["render", _sfc_render], ["__file", "/Users/<USER>/Documents/HBuilderProjects/WidgetDemo2/pages/index/index.vue"]]);
  __definePage("pages/index/index", PagesIndexIndex);
  const _sfc_main = {
    onLaunch: function() {
      formatAppLog("log", "at App.vue:4", "App Launch");
    },
    onShow: function() {
      formatAppLog("log", "at App.vue:7", "App Show");
    },
    onHide: function() {
      formatAppLog("log", "at App.vue:10", "App Hide");
    }
  };
  const App = /* @__PURE__ */ _export_sfc(_sfc_main, [["__file", "/Users/<USER>/Documents/HBuilderProjects/WidgetDemo2/App.vue"]]);
  function createApp() {
    const app = vue.createVueApp(App);
    return {
      app
    };
  }
  const { app: __app__, Vuex: __Vuex__, Pinia: __Pinia__ } = createApp();
  uni.Vuex = __Vuex__;
  uni.Pinia = __Pinia__;
  __app__.provide("__globalStyles", __uniConfig.styles);
  __app__._component.mpType = "app";
  __app__._component.render = () => {
  };
  __app__.mount("#app");
})(Vue);
