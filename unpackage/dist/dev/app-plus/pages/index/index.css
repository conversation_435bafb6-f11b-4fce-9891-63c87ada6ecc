
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.25rem;
  min-height: 100vh;
  background-color: #f8f9fa;
}
.logo {
  height: 3.75rem;
  width: 3.75rem;
  margin-bottom: 0.9375rem;
}
.text-area {
  display: flex;
  justify-content: center;
  margin-bottom: 1.25rem;
}
.title {
  font-size: 1.5rem;
  color: #333;
  font-weight: bold;
}

/* Widget控制面板样式 */
.widget-panel {
  width: 100%;
  max-width: 21.875rem;
  background-color: #fff;
  border-radius: 0.625rem;
  padding: 1.25rem;
  box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
}
.panel-title {
  font-size: 1.125rem;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 1.25rem;
  display: block;
}

/* 状态区域 */
.status-section {
  margin-bottom: 1.25rem;
  padding: 0.9375rem;
  background-color: #f8f9fa;
  border-radius: 0.46875rem;
}
.section-title {
  font-size: 0.875rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.625rem;
  display: block;
}
.status-text {
  font-size: 0.8125rem;
  padding: 0.3125rem 0.625rem;
  border-radius: 0.3125rem;
  display: inline-block;
}
.status-available {
  background-color: #d4edda;
  color: #155724;
}
.status-unavailable {
  background-color: #f8d7da;
  color: #721c24;
}

/* 输入区域 */
.input-section {
  margin-bottom: 1.25rem;
}
.input-field {
  width: 100%;
  height: 2.5rem;
  padding: 0 0.9375rem;
  margin-bottom: 0.625rem;
  border: 0.0625rem solid #e9ecef;
  border-radius: 0.3125rem;
  font-size: 0.875rem;
  background-color: #fff;
}
.textarea-field {
  width: 100%;
  min-height: 3.75rem;
  padding: 0.625rem 0.9375rem;
  border: 0.0625rem solid #e9ecef;
  border-radius: 0.3125rem;
  font-size: 0.875rem;
  background-color: #fff;
  resize: vertical;
}

/* 按钮区域 */
.button-section {
  margin-bottom: 1.25rem;
}
.btn {
  width: 100%;
  height: 2.5rem;
  margin-bottom: 0.625rem;
  border-radius: 0.3125rem;
  font-size: 0.875rem;
  font-weight: bold;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}
.btn-primary {
  background-color: #007aff;
  color: #fff;
}
.btn-primary:disabled {
  background-color: #ccc;
  color: #666;
}
.btn-secondary {
  background-color: #6c757d;
  color: #fff;
}
.btn-secondary:disabled {
  background-color: #ccc;
  color: #666;
}
.btn-info {
  background-color: #17a2b8;
  color: #fff;
}

/* 数据显示区域 */
.data-section {
  padding: 0.9375rem;
  background-color: #f8f9fa;
  border-radius: 0.46875rem;
}
.data-item {
  display: flex;
  margin-bottom: 0.625rem;
  align-items: flex-start;
}
.data-label {
  font-size: 0.8125rem;
  color: #666;
  min-width: 4.375rem;
  font-weight: bold;
}
.data-value {
  font-size: 0.8125rem;
  color: #333;
  flex: 1;
  word-break: break-all;
}
