/**
 * iOS Widget Plugin for uni-app
 * 提供与iOS Widget交互的JavaScript接口
 */

const WidgetPlugin = uni.requireNativePlugin('WidgetPlugin');

class WidgetManager {
    /**
     * 检查Widget是否可用
     * @returns {Promise<boolean>}
     */
    static isAvailable() {
        return new Promise((resolve, reject) => {
            if (!WidgetPlugin) {
                resolve(false);
                return;
            }
            
            WidgetPlugin.isWidgetAvailable((result) => {
                resolve(result.available || false);
            });
        });
    }
    
    /**
     * 更新Widget数据
     * @param {Object} data - Widget数据
     * @param {string} data.title - 标题
     * @param {string} data.content - 内容
     * @param {string} data.timestamp - 时间戳
     * @returns {Promise<Object>}
     */
    static updateData(data) {
        return new Promise((resolve, reject) => {
            if (!WidgetPlugin) {
                reject(new Error('Widget plugin not available'));
                return;
            }
            
            const options = {
                title: data.title || '',
                content: data.content || '',
                timestamp: data.timestamp || new Date().toLocaleString()
            };
            
            WidgetPlugin.updateWidgetData(options, (result) => {
                if (result.success) {
                    resolve(result);
                } else {
                    reject(new Error(result.message || 'Update failed'));
                }
            });
        });
    }
    
    /**
     * 获取Widget数据
     * @returns {Promise<Object>}
     */
    static getData() {
        return new Promise((resolve, reject) => {
            if (!WidgetPlugin) {
                reject(new Error('Widget plugin not available'));
                return;
            }
            
            WidgetPlugin.getWidgetData((result) => {
                resolve(result);
            });
        });
    }
    
    /**
     * 更新Widget标题
     * @param {string} title - 新标题
     * @returns {Promise<Object>}
     */
    static updateTitle(title) {
        return this.updateData({ title });
    }
    
    /**
     * 更新Widget内容
     * @param {string} content - 新内容
     * @returns {Promise<Object>}
     */
    static updateContent(content) {
        return this.updateData({ content });
    }
    
    /**
     * 批量更新Widget数据
     * @param {string} title - 标题
     * @param {string} content - 内容
     * @returns {Promise<Object>}
     */
    static updateAll(title, content) {
        return this.updateData({
            title,
            content,
            timestamp: new Date().toLocaleString()
        });
    }
}

export default WidgetManager;
