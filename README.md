# uni-app iOS Widget 集成演示

这是一个完整的uni-app项目，演示如何集成iOS Widget小组件功能。

## 功能特性

- ✅ iOS Widget Extension集成
- ✅ uni-app与Widget数据共享
- ✅ 实时更新Widget内容
- ✅ 完整的JavaScript API接口
- ✅ 美观的演示界面

## 项目结构

```
WidgetDemo2/
├── nativeplugins/                 # 原生插件目录
│   └── WidgetPlugin/             # Widget插件
│       ├── package.json          # 插件配置
│       └── ios/                  # iOS原生代码
│           ├── WidgetPlugin.h    # 插件头文件
│           ├── WidgetPlugin.m    # 插件实现
│           └── WidgetExtension/  # Widget Extension
│               ├── WidgetExtension.swift  # Widget主要代码
│               └── Info.plist    # Widget配置
├── js_sdk/                       # JavaScript SDK
│   └── WidgetPlugin/            # Widget JS接口
│       ├── index.js             # 主要接口文件
│       └── package.json         # SDK配置
├── pages/                       # 页面文件
│   └── index/
│       └── index.vue           # 主页面（演示界面）
├── manifest.json               # 应用配置（已配置插件）
└── README.md                   # 说明文档
```

## 核心功能

### 1. Widget数据管理
- 更新Widget标题和内容
- 获取当前Widget数据
- 检查Widget可用性状态

### 2. 数据共享机制
- 使用App Groups实现应用与Widget间数据共享
- 自动同步数据更新
- 支持实时刷新Widget显示

### 3. JavaScript API

```javascript
import WidgetManager from '@/js_sdk/WidgetPlugin/index.js'

// 检查Widget是否可用
const available = await WidgetManager.isAvailable()

// 更新Widget数据
await WidgetManager.updateAll('标题', '内容')

// 获取Widget数据
const data = await WidgetManager.getData()
```

## 使用说明

### 1. 开发环境要求
- HBuilderX 3.0+
- iOS 14.0+
- Xcode 12.0+

### 2. 配置步骤

1. **App Groups配置**
   - 在Apple Developer后台创建App Groups
   - 配置Bundle ID和App Groups标识符
   - 确保主应用和Widget Extension都启用相同的App Groups

2. **项目配置**
   - manifest.json已配置好插件和权限
   - 确认App Groups标识符与开发者账号一致

3. **编译打包**
   - 使用HBuilderX制作自定义调试基座
   - 或直接打包发布版本

### 3. Widget添加方法
1. 在iOS设备上长按桌面空白处
2. 点击左上角"+"按钮
3. 搜索并选择你的应用
4. 选择Widget样式并添加到桌面

## 技术实现

### 1. 原生插件架构
- 基于uni-app原生插件规范
- 使用Objective-C实现iOS端功能
- 通过DCUniModule提供JavaScript接口

### 2. Widget Extension
- 使用SwiftUI构建Widget界面
- 实现TimelineProvider提供数据
- 支持小尺寸和中等尺寸Widget

### 3. 数据共享
- 使用NSUserDefaults + App Groups
- 实现应用与Widget间数据同步
- 支持实时更新和刷新

## 自定义开发

### 修改Widget样式
编辑 `nativeplugins/WidgetPlugin/ios/WidgetExtension/WidgetExtension.swift` 文件中的SwiftUI代码。

### 扩展功能
在 `nativeplugins/WidgetPlugin/ios/WidgetPlugin.m` 中添加新的原生方法，并在 `js_sdk/WidgetPlugin/index.js` 中添加对应的JavaScript接口。

### 修改App Groups
1. 更新 `manifest.json` 中的App Groups配置
2. 同步更新原生代码中的App Groups标识符

## 注意事项

1. **iOS版本要求**：Widget功能需要iOS 14.0及以上版本
2. **证书配置**：需要正确配置App Groups权限
3. **数据限制**：Widget数据应保持轻量，避免存储大量数据
4. **更新频率**：系统会限制Widget的更新频率
5. **调试方法**：建议使用真机调试，模拟器可能存在限制

## 常见问题

**Q: Widget不显示数据？**
A: 检查App Groups配置是否正确，确保主应用和Widget使用相同的标识符。

**Q: 无法添加Widget到桌面？**
A: 确认应用已正确安装，且iOS版本支持Widget功能。

**Q: 数据更新不及时？**
A: iOS系统会控制Widget更新频率，可以尝试重新进入应用触发更新。

## 许可证

MIT License
