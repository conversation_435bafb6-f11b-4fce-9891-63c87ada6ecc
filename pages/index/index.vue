<template>
  <view class="content">
    <image class="logo" src="/static/logo.png"></image>
    <view class="text-area">
      <text class="title">{{ title }}</text>
    </view>

    <!-- Widget控制面板 -->
    <view class="widget-panel">
      <text class="panel-title">iOS Widget 控制面板</text>

      <!-- Widget状态 -->
      <view class="status-section">
        <text class="section-title">Widget状态</text>
        <text
          class="status-text"
          :class="widgetAvailable ? 'status-available' : 'status-unavailable'"
        >
          {{ widgetAvailable ? "✅ Widget可用" : "❌ Widget不可用" }}
        </text>
        <text v-if="!widgetAvailable" class="status-tip">
          💡 如需使用Widget功能，请制作包含原生插件的自定义调试基座
        </text>
      </view>

      <!-- 数据输入 -->
      <view class="input-section">
        <text class="section-title">更新Widget数据</text>
        <input
          class="input-field"
          v-model="widgetTitle"
          placeholder="输入Widget标题"
          maxlength="50"
        />
        <textarea
          class="textarea-field"
          v-model="widgetContent"
          placeholder="输入Widget内容"
          maxlength="200"
        />
      </view>

      <!-- 操作按钮 -->
      <view class="button-section">
        <button
          class="btn btn-primary"
          @click="updateWidget"
          :disabled="!widgetAvailable"
        >
          更新Widget
        </button>
        <button
          class="btn btn-secondary"
          @click="getWidgetData"
          :disabled="!widgetAvailable"
        >
          获取Widget数据
        </button>
        <button class="btn btn-info" @click="checkWidgetStatus">
          检查Widget状态
        </button>
      </view>

      <!-- 当前Widget数据显示 -->
      <view class="data-section" v-if="currentWidgetData">
        <text class="section-title">当前Widget数据</text>
        <view class="data-item">
          <text class="data-label">标题：</text>
          <text class="data-value">{{ currentWidgetData.title }}</text>
        </view>
        <view class="data-item">
          <text class="data-label">内容：</text>
          <text class="data-value">{{ currentWidgetData.content }}</text>
        </view>
        <view class="data-item">
          <text class="data-label">更新时间：</text>
          <text class="data-value">{{ currentWidgetData.timestamp }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import WidgetManager from "@/js_sdk/WidgetPlugin/index.js";

export default {
  data() {
    return {
      title: "Widget Demo",
      widgetAvailable: false,
      widgetTitle: "Hello Widget",
      widgetContent: "这是来自uni-app的Widget内容",
      currentWidgetData: null,
    };
  },
  async onLoad() {
    // 检查是否在包含插件的基座中运行
    if (uni.requireNativePlugin) {
      await this.checkWidgetStatus();
      await this.getWidgetData();
    } else {
      console.log("当前基座不支持原生插件，请制作自定义基座");
      this.widgetAvailable = false;
    }
  },
  methods: {
    // 检查Widget状态
    async checkWidgetStatus() {
      try {
        // 检查原生插件是否可用
        const WidgetPlugin = uni.requireNativePlugin("WidgetPlugin");
        if (!WidgetPlugin) {
          console.log("WidgetPlugin not available in current base");
          this.widgetAvailable = false;
          return;
        }

        this.widgetAvailable = await WidgetManager.isAvailable();
        console.log("Widget available:", this.widgetAvailable);
      } catch (error) {
        console.error("Check widget status error:", error);
        this.widgetAvailable = false;
      }
    },

    // 更新Widget数据
    async updateWidget() {
      if (!this.widgetAvailable) {
        uni.showToast({
          title: "Widget不可用",
          icon: "none",
        });
        return;
      }

      try {
        uni.showLoading({
          title: "更新中...",
        });

        const result = await WidgetManager.updateAll(
          this.widgetTitle,
          this.widgetContent
        );

        uni.hideLoading();
        uni.showToast({
          title: "更新成功",
          icon: "success",
        });

        // 更新成功后获取最新数据
        await this.getWidgetData();
      } catch (error) {
        uni.hideLoading();
        console.error("Update widget error:", error);
        uni.showToast({
          title: "更新失败",
          icon: "none",
        });
      }
    },

    // 获取Widget数据
    async getWidgetData() {
      if (!this.widgetAvailable) {
        return;
      }

      try {
        const data = await WidgetManager.getData();
        this.currentWidgetData = data;
        console.log("Widget data:", data);
      } catch (error) {
        console.error("Get widget data error:", error);
      }
    },
  },
};
</script>

<style>
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
  min-height: 100vh;
  background-color: #f8f9fa;
}

.logo {
  height: 120rpx;
  width: 120rpx;
  margin-bottom: 30rpx;
}

.text-area {
  display: flex;
  justify-content: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 48rpx;
  color: #333;
  font-weight: bold;
}

/* Widget控制面板样式 */
.widget-panel {
  width: 100%;
  max-width: 700rpx;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.panel-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 40rpx;
  display: block;
}

/* 状态区域 */
.status-section {
  margin-bottom: 40rpx;
  padding: 30rpx;
  background-color: #f8f9fa;
  border-radius: 15rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.status-text {
  font-size: 26rpx;
  padding: 10rpx 20rpx;
  border-radius: 10rpx;
  display: inline-block;
}

.status-available {
  background-color: #d4edda;
  color: #155724;
}

.status-unavailable {
  background-color: #f8d7da;
  color: #721c24;
}

.status-tip {
  font-size: 24rpx;
  color: #6c757d;
  margin-top: 15rpx;
  display: block;
  line-height: 1.4;
}

/* 输入区域 */
.input-section {
  margin-bottom: 40rpx;
}

.input-field {
  width: 100%;
  height: 80rpx;
  padding: 0 30rpx;
  margin-bottom: 20rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 10rpx;
  font-size: 28rpx;
  background-color: #fff;
}

.textarea-field {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx 30rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 10rpx;
  font-size: 28rpx;
  background-color: #fff;
  resize: vertical;
}

/* 按钮区域 */
.button-section {
  margin-bottom: 40rpx;
}

.btn {
  width: 100%;
  height: 80rpx;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: #007aff;
  color: #fff;
}

.btn-primary:disabled {
  background-color: #ccc;
  color: #666;
}

.btn-secondary {
  background-color: #6c757d;
  color: #fff;
}

.btn-secondary:disabled {
  background-color: #ccc;
  color: #666;
}

.btn-info {
  background-color: #17a2b8;
  color: #fff;
}

/* 数据显示区域 */
.data-section {
  padding: 30rpx;
  background-color: #f8f9fa;
  border-radius: 15rpx;
}

.data-item {
  display: flex;
  margin-bottom: 20rpx;
  align-items: flex-start;
}

.data-label {
  font-size: 26rpx;
  color: #666;
  min-width: 140rpx;
  font-weight: bold;
}

.data-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
}
</style>
