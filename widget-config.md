# iOS Widget 配置指南

## 1. Apple Developer 配置

### App Groups 设置

1. 登录 [Apple Developer](https://developer.apple.com)
2. 进入 "Certificates, Identifiers & Profiles"
3. 选择 "Identifiers" → "App Groups"
4. 点击 "+" 创建新的 App Group
5. 输入描述和标识符（如：`group.widgetdemo.shared`）

### App ID 配置

1. 在 "Identifiers" → "App IDs" 中找到你的应用
2. 编辑应用配置
3. 在 "Capabilities" 中启用 "App Groups"
4. 选择刚创建的 App Group

### Widget Extension App ID

1. 创建新的 App ID 用于 Widget Extension
2. Bundle ID 格式：`com.yourcompany.yourapp.WidgetExtension`
3. 同样启用 "App Groups" 并选择相同的 App Group

## 2. Xcode 项目配置

### 主应用配置

1. 在项目设置中选择主应用 Target
2. 进入 "Signing & Capabilities"
3. 添加 "App Groups" capability
4. 选择对应的 App Group

### Widget Extension 配置

1. 添加新的 Widget Extension Target
2. 配置 Bundle ID 为：`主应用BundleID.WidgetExtension`
3. 添加 "App Groups" capability
4. 选择相同的 App Group

## 3. uni-app 配置

### manifest.json 配置

```json
{
  "app-plus": {
    "nativePlugins": {
      "WidgetPlugin": {
        "hooksClass": "",
        "plugins": [
          {
            "type": "module",
            "name": "WidgetPlugin",
            "class": "WidgetPlugin"
          }
        ]
      }
    },
    "distribute": {
      "ios": {
        "capabilities": {
          "entitlements": {
            "com.apple.security.application-groups": [
              "group.com.wangxiaobao.iwangke"
            ]
          }
        }
      }
    }
  }
}
```

### 插件配置文件

`nativeplugins/WidgetPlugin/package.json`:

```json
{
  "name": "WidgetPlugin",
  "id": "WidgetPlugin",
  "_dp_nativeplugin": {
    "ios": {
      "capabilities": {
        "entitlements": {
          "com.apple.security.application-groups": [
            "group.com.wangxiaobao.iwangke"
          ]
        }
      }
    }
  }
}
```

## 4. 代码中的配置

### 原生代码配置

在 `WidgetPlugin.m` 和 `WidgetExtension.swift` 中：

```objective-c
// App Groups 标识符
NSString *appGroupIdentifier = @"group.com.wangxiaobao.iwangke";
```

```swift
// Swift中的配置
let sharedDefaults = UserDefaults(suiteName: "group.com.wangxiaobao.iwangke")
```

## 5. 打包配置

### 证书配置

1. 确保开发证书包含 App Groups 权限
2. 发布证书同样需要包含 App Groups 权限
3. Provisioning Profile 需要包含 App Groups 配置

### HBuilderX 打包

1. 使用自定义调试基座进行开发测试
2. 正式打包时选择对应的证书和描述文件
3. 确保 Bundle ID 与开发者后台配置一致

## 6. 常见配置错误

### App Groups 不匹配

- 主应用和 Widget Extension 必须使用相同的 App Groups 标识符
- 检查代码中的标识符是否与配置一致

### Bundle ID 配置错误

- Widget Extension 的 Bundle ID 必须是主应用的子 ID
- 格式：`主应用ID.WidgetExtension`

### 权限配置缺失

- 确保在 Apple Developer 后台正确配置了 App Groups
- 检查 Provisioning Profile 是否包含 App Groups 权限

## 7. 调试建议

### 开发阶段

1. 使用真机调试，避免模拟器限制
2. 检查控制台日志确认数据共享是否正常
3. 使用 Xcode 直接调试 Widget Extension

### 测试阶段

1. 测试不同尺寸的 Widget 显示效果
2. 验证数据更新的及时性
3. 测试应用后台时 Widget 的行为

### 发布前检查

1. 确认所有配置文件中的标识符一致
2. 测试正式打包版本的 Widget 功能
3. 验证 App Store Connect 中的配置

## 8. 版本兼容性

### iOS 版本要求

- Widget 功能需要 iOS 14.0+
- 建议在代码中添加版本检查

### uni-app 版本

- 建议使用 HBuilderX 3.0+
- 确保支持原生插件功能

### Xcode 版本

- 需要 Xcode 12.0+支持 Widget 开发
- 建议使用最新版本 Xcode

## 9. 性能优化

### 数据存储

- 使用轻量级数据存储
- 避免存储大量数据到 App Groups

### 更新频率

- 合理设置 Widget 更新时间间隔
- 避免频繁更新消耗系统资源

### 界面设计

- 保持 Widget 界面简洁
- 优化不同尺寸下的显示效果
