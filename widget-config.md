# iOS Widget 配置指南

## 1. Apple Developer 配置

### App Groups 设置
1. 登录 [Apple Developer](https://developer.apple.com)
2. 进入 "Certificates, Identifiers & Profiles"
3. 选择 "Identifiers" → "App Groups"
4. 点击 "+" 创建新的App Group
5. 输入描述和标识符（如：`group.widgetdemo.shared`）

### App ID 配置
1. 在 "Identifiers" → "App IDs" 中找到你的应用
2. 编辑应用配置
3. 在 "Capabilities" 中启用 "App Groups"
4. 选择刚创建的App Group

### Widget Extension App ID
1. 创建新的App ID用于Widget Extension
2. Bundle ID格式：`com.yourcompany.yourapp.WidgetExtension`
3. 同样启用 "App Groups" 并选择相同的App Group

## 2. Xcode 项目配置

### 主应用配置
1. 在项目设置中选择主应用Target
2. 进入 "Signing & Capabilities"
3. 添加 "App Groups" capability
4. 选择对应的App Group

### Widget Extension配置
1. 添加新的Widget Extension Target
2. 配置Bundle ID为：`主应用BundleID.WidgetExtension`
3. 添加 "App Groups" capability
4. 选择相同的App Group

## 3. uni-app 配置

### manifest.json 配置
```json
{
  "app-plus": {
    "nativePlugins": {
      "WidgetPlugin": {
        "hooksClass": "",
        "plugins": [
          {
            "type": "module",
            "name": "WidgetPlugin",
            "class": "WidgetPlugin"
          }
        ]
      }
    },
    "distribute": {
      "ios": {
        "capabilities": {
          "entitlements": {
            "com.apple.security.application-groups": ["group.widgetdemo.shared"]
          }
        }
      }
    }
  }
}
```

### 插件配置文件
`nativeplugins/WidgetPlugin/package.json`:
```json
{
  "name": "WidgetPlugin",
  "id": "WidgetPlugin",
  "_dp_nativeplugin": {
    "ios": {
      "capabilities": {
        "entitlements": {
          "com.apple.security.application-groups": ["group.widgetdemo.shared"]
        }
      }
    }
  }
}
```

## 4. 代码中的配置

### 原生代码配置
在 `WidgetPlugin.m` 和 `WidgetExtension.swift` 中：
```objective-c
// App Groups 标识符
NSString *appGroupIdentifier = @"group.widgetdemo.shared";
```

```swift
// Swift中的配置
let sharedDefaults = UserDefaults(suiteName: "group.widgetdemo.shared")
```

## 5. 打包配置

### 证书配置
1. 确保开发证书包含App Groups权限
2. 发布证书同样需要包含App Groups权限
3. Provisioning Profile需要包含App Groups配置

### HBuilderX 打包
1. 使用自定义调试基座进行开发测试
2. 正式打包时选择对应的证书和描述文件
3. 确保Bundle ID与开发者后台配置一致

## 6. 常见配置错误

### App Groups不匹配
- 主应用和Widget Extension必须使用相同的App Groups标识符
- 检查代码中的标识符是否与配置一致

### Bundle ID配置错误
- Widget Extension的Bundle ID必须是主应用的子ID
- 格式：`主应用ID.WidgetExtension`

### 权限配置缺失
- 确保在Apple Developer后台正确配置了App Groups
- 检查Provisioning Profile是否包含App Groups权限

## 7. 调试建议

### 开发阶段
1. 使用真机调试，避免模拟器限制
2. 检查控制台日志确认数据共享是否正常
3. 使用Xcode直接调试Widget Extension

### 测试阶段
1. 测试不同尺寸的Widget显示效果
2. 验证数据更新的及时性
3. 测试应用后台时Widget的行为

### 发布前检查
1. 确认所有配置文件中的标识符一致
2. 测试正式打包版本的Widget功能
3. 验证App Store Connect中的配置

## 8. 版本兼容性

### iOS版本要求
- Widget功能需要iOS 14.0+
- 建议在代码中添加版本检查

### uni-app版本
- 建议使用HBuilderX 3.0+
- 确保支持原生插件功能

### Xcode版本
- 需要Xcode 12.0+支持Widget开发
- 建议使用最新版本Xcode

## 9. 性能优化

### 数据存储
- 使用轻量级数据存储
- 避免存储大量数据到App Groups

### 更新频率
- 合理设置Widget更新时间间隔
- 避免频繁更新消耗系统资源

### 界面设计
- 保持Widget界面简洁
- 优化不同尺寸下的显示效果
